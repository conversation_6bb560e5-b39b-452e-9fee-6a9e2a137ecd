#!/usr/bin/env python3
"""
Transkription + hierarchische Zusammenfassung eines Videos mit lokalem LLM (Ollama)

Features
- Audio-Extraktion via ffmpeg
- Transkription mit faster-whisper (Word/Segment-Timestamps)
- Chunking nach Segmenten mit Token-Heuristik
- Map-Reduce-Zusammenfassung (Chunk -> Summary -> Final)
- Ausgabe als TXT, SRT, JSON, Markdown

Voraussetzungen
- ffmpeg installiert (im PATH)
- Python Pakete: faster-whisper, srt, requests, rich
- Lokales LLM via Ollama (https://ollama.com) mit passendem Modell (z.B. "llama3.1:8b")

Beispielaufruf
$ python video_transcribe_summarize_local_llm.py input.mp4 \
    --whisper-model large-v3 --llm-model llama3.1:8b \
    --language de --target-chunk-tokens 1600 --out-dir outputs
"""

import argparse
import json
import logging
import math
import os
import re
import subprocess
import sys
import traceback
from dataclasses import dataclass
from datetime import timedelta
from typing import List, Dict, Any, Optional

import requests
import srt
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from faster_whisper import WhisperModel


# ----------------------------- Utils -----------------------------

def setup_logging(verbose: bool):
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format="%(message)s",
        datefmt="%H:%M:%S",
        handlers=[RichHandler(rich_tracebacks=True, markup=True)],
    )


def run_ffmpeg_extract_audio(video_path: str, audio_path: str, sample_rate: int = 16000):
    cmd = [
        "ffmpeg",
        "-y",
        "-i",
        video_path,
        "-ac",
        "1",
        "-ar",
        str(sample_rate),
        "-f",
        "wav",
        audio_path,
    ]
    logging.info("[bold]FFmpeg[/bold]: Extrahiere Audio…")
    try:
        subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except FileNotFoundError:
        logging.error("ffmpeg nicht gefunden. Bitte installieren und im PATH verfügbar machen.")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        logging.error("FFmpeg Fehler: %s", e.stderr.decode(errors="ignore"))
        sys.exit(1)


def to_timedelta(seconds: float) -> timedelta:
    return timedelta(seconds=max(0.0, float(seconds)))


@dataclass
class Segment:
    start: float
    end: float
    text: str


@dataclass
class Chunk:
    index: int
    start: float
    end: float
    text: str


# ----------------------------- Transcribe -----------------------------

def transcribe_audio(
    audio_path: str,
    whisper_model: str = "large-v3",
    device: str = "auto",
    compute_type: Optional[str] = None,
    language: Optional[str] = None,
    beam_size: int = 5,
    vad_filter: bool = True,
) -> List[Segment]:
    """Transkribiert Audio mit faster-whisper und liefert Segmente."""
    if device == "auto":
        # faster-whisper schaltet selbst, wir setzen nur compute_type
        import torch
        has_cuda = torch.cuda.is_available()
        device = "cuda" if has_cuda else "cpu"
        if compute_type is None:
            compute_type = "float16" if has_cuda else "int8"
    else:
        if compute_type is None:
            compute_type = "float16" if device == "cuda" else "int8"

    logging.info(
        f"Whisper lade Modell '{whisper_model}' (device={device}, compute_type={compute_type})…"
    )
    model = WhisperModel(whisper_model, device=device, compute_type=compute_type)

    logging.info("Transkribiere… (dies kann je nach Länge dauern)")
    segments_iter, info = model.transcribe(
        audio_path,
        language=language,
        beam_size=beam_size,
        vad_filter=vad_filter,
        word_timestamps=False,
        condition_on_previous_text=True,
    )

    segments: List[Segment] = []
    for seg in segments_iter:
        text = seg.text.strip()
        if text:
            segments.append(Segment(start=float(seg.start), end=float(seg.end), text=text))

    logging.info(
        f"Transkription fertig. Sprache: {getattr(info, 'language', 'unbekannt')} – Segmente: {len(segments)}"
    )
    return segments


# ----------------------------- SRT / TXT -----------------------------

def write_srt(segments: List[Segment], srt_path: str):
    try:
        subs = []
        for i, seg in enumerate(segments, start=1):
            subs.append(
                srt.Subtitle(
                    index=i,
                    start=to_timedelta(seg.start),
                    end=to_timedelta(seg.end),
                    content=seg.text,
                )
            )
        with open(srt_path, "w", encoding="utf-8") as f:
            f.write(srt.compose(subs))
        logging.info(f"SRT gespeichert: {srt_path}")
    except Exception as e:
        logging.error(f"Fehler beim Speichern der SRT-Datei: {e}")
        raise


def write_transcript_txt(segments: List[Segment], txt_path: str):
    try:
        with open(txt_path, "w", encoding="utf-8") as f:
            for seg in segments:
                f.write(f"[{seg.start:8.2f}–{seg.end:8.2f}] {seg.text}\n")
        logging.info(f"Transkript TXT gespeichert: {txt_path}")
    except Exception as e:
        logging.error(f"Fehler beim Speichern der TXT-Datei: {e}")
        raise


# ----------------------------- Chunking -----------------------------

def approximate_token_count(text: str) -> int:
    """Grobe Heuristik: ~4 Zeichen pro Token."""
    return max(1, math.ceil(len(text) / 4))


def build_chunks_from_segments(
    segments: List[Segment], target_chunk_tokens: int = 1600, hard_max_tokens: int = 2048
) -> List[Chunk]:
    chunks: List[Chunk] = []
    cur_text_parts: List[str] = []
    cur_tokens = 0
    cur_start: Optional[float] = None
    cur_end: Optional[float] = None

    def flush_chunk():
        nonlocal cur_text_parts, cur_tokens, cur_start, cur_end
        if cur_text_parts:
            idx = len(chunks)
            text = re.sub(r"\s+", " ", " ".join(cur_text_parts).strip())
            chunks.append(Chunk(index=idx, start=cur_start or 0.0, end=cur_end or 0.0, text=text))
            cur_text_parts = []
            cur_tokens = 0
            cur_start = None
            cur_end = None

    for seg in segments:
        seg_text = seg.text.strip()
        seg_tokens = approximate_token_count(seg_text)

        if cur_start is None:
            cur_start = seg.start

        # Falls ein einzelnes Segment schon zu groß wäre, forcieren wir eigenen Chunk
        if seg_tokens >= hard_max_tokens:
            flush_chunk()
            chunks.append(Chunk(index=len(chunks), start=seg.start, end=seg.end, text=seg_text))
            continue

        if cur_tokens + seg_tokens > target_chunk_tokens:
            # schließen aktuellen Chunk und starten neuen
            flush_chunk()

        # hinzufügen
        cur_text_parts.append(seg_text)
        cur_tokens += seg_tokens
        cur_end = seg.end

    flush_chunk()
    logging.info(f"Chunking: {len(chunks)} Chunks erzeugt (Ziel ~{target_chunk_tokens} Tokens)")
    return chunks


# ----------------------------- Ollama LLM -----------------------------

def ollama_generate(
    prompt: str,
    model: str = "llama3.1:8b",
    num_ctx: int = 8192,
    num_predict: int = 800,
    temperature: float = 0.2,
    base_url: str = "http://localhost:11434",
    timeout: int = 600,
) -> str:
    url = f"{base_url}/api/generate"
    payload = {
        "model": model,
        "prompt": prompt,
        "options": {
            "temperature": temperature,
            "num_ctx": num_ctx,
            "num_predict": num_predict,
        },
        "stream": False,
    }
    try:
        r = requests.post(url, json=payload, timeout=timeout)
        r.raise_for_status()
        data = r.json()
        return data.get("response", "").strip()
    except requests.RequestException as e:
        logging.error("Fehler beim Aufruf von Ollama: %s", e)
        raise


# ----------------------------- Prompts -----------------------------

def build_map_prompt(chunk: Chunk) -> str:
    return (
        "Du bist ein präziser Analyst für Videotranskripte. "
        "Fasse den folgenden Abschnitt in gutem, kompaktem Deutsch zusammen. "
        "Behalte wichtige Fakten, Zahlen, Namen und Begriffe bei. Antworte ausschließlich als JSON mit den Feldern: "
        "{\n"
        "  \"chunk_index\": <int>,\n"
        "  \"zeitraum\": \"HH:MM:SS–HH:MM:SS\",\n"
        "  \"kernaussage\": \"<1-2 Sätze>\",\n"
        "  \"stichpunkte\": ["
        "\"<max 6 Bullets>\"],\n"
        "  \"personen\": [\"<Namen>\"],\n"
        "  \"themen\": [\"<Schlagworte>\"],\n"
        "  \"zitierwuerdige_zitate\": [\"<max 2 kurze Zitate>\"]\n"
        "}. "
        f"Abschnitt (Start {seconds_to_hhmmss(chunk.start)}, Ende {seconds_to_hhmmss(chunk.end)}):\n" 
        f"""""{chunk.text}"""""
    )


def build_reduce_prompt(map_json_list: List[Dict[str, Any]]) -> str:
    packed = json.dumps(map_json_list, ensure_ascii=False)
    return (
        "Du bist ein erfahrener Redakteur. Du erhältst JSON-Zusammenfassungen von Videoteilen. "
        "Erzeuge eine finale, prägnante Auswertung in DEUTSCH als JSON mit: "
        "{\n"
        "  \"laufzeit\": \"<gesamt in Minuten>\",\n"
        "  \"kapitel\": [ { \"von\": \"HH:MM:SS\", \"bis\": \"HH:MM:SS\", \"titel\": \"<kurz>\", \"inhalt\": \"<2-3 Sätze>\" } ],\n"
        "  \"highlights\": [\"<konkrete, zitierfähige Erkenntnisse>\"],\n"
        "  \"takeaways\": [\"<praktische Schlussfolgerungen>\"],\n"
        "  \"tl_dr\": \"<ein Satz>\"\n"
        "}. Nutze die Chunks inhaltlich, keine Wiederholungen, keine Spekulationen.\n\n"
        f"Chunk-Zusammenfassungen:\n{packed}"
    )


def seconds_to_hhmmss(s: float) -> str:
    h = int(s // 3600)
    m = int((s % 3600) // 60)
    sec = int(s % 60)
    return f"{h:02d}:{m:02d}:{sec:02d}"


# ----------------------------- Pipeline -----------------------------

def map_reduce_summarize(
    chunks: List[Chunk],
    llm_model: str,
    num_ctx: int = 8192,
    map_num_predict: int = 700,
    reduce_num_predict: int = 900,
    temperature: float = 0.2,
) -> Dict[str, Any]:
    logging.info(f"Beginne Map-Reduce mit {len(chunks)} Chunks und Modell {llm_model}")
    
    # MAP
    map_results: List[Dict[str, Any]] = []
    for ch in chunks:
        logging.info(f"Verarbeite Chunk {ch.index}/{len(chunks)} ({seconds_to_hhmmss(ch.start)}-{seconds_to_hhmmss(ch.end)})")
        prompt = build_map_prompt(ch)
        out = ollama_generate(
            prompt,
            model=llm_model,
            num_ctx=num_ctx,
            num_predict=map_num_predict,
            temperature=temperature,
        )
        try:
            data = json.loads(out)
        except json.JSONDecodeError:
            logging.warning(
                "Chunk %d: Ausgabe war kein valides JSON. Rohtext wird verpackt.", ch.index
            )
            data = {
                "chunk_index": ch.index,
                "zeitraum": f"{seconds_to_hhmmss(ch.start)}–{seconds_to_hhmmss(ch.end)}",
                "kernaussage": out.strip()[:400],
                "stichpunkte": [],
                "personen": [],
                "themen": [],
                "zitierwuerdige_zitate": [],
            }
        map_results.append(data)

    # REDUCE
    logging.info("Beginne finale Zusammenfassung...")
    reduce_prompt = build_reduce_prompt(map_results)
    reduce_out = ollama_generate(
        reduce_prompt,
        model=llm_model,
        num_ctx=num_ctx,
        num_predict=reduce_num_predict,
        temperature=temperature,
    )
    try:
        final_json = json.loads(reduce_out)
    except json.JSONDecodeError:
        logging.warning("Finale Ausgabe war kein valides JSON. Fasse als Freitext zusammen.")
        final_json = {"freitext": reduce_out.strip()}

    return {"chunks": [c.__dict__ for c in chunks], "map": map_results, "final": final_json}


def write_markdown_summary(result: Dict[str, Any], path: str):
    final = result.get("final", {})
    lines = ["# Zusammenfassung", ""]
    if "laufzeit" in final:
        lines.append(f"**Laufzeit:** {final['laufzeit']}")
        lines.append("")
    if "tl_dr" in final:
        lines.append(f"**TL;DR:** {final['tl_dr']}")
        lines.append("")

    if "kapitel" in final and isinstance(final["kapitel"], list):
        lines.append("## Kapitel")
        for k in final["kapitel"]:
            lines.append(
                f"- **{k.get('von', '')}–{k.get('bis', '')}** – {k.get('titel', '')}: {k.get('inhalt', '')}"
            )
        lines.append("")

    if "highlights" in final and isinstance(final["highlights"], list):
        lines.append("## Highlights")
        for h in final["highlights"]:
            lines.append(f"- {h}")
        lines.append("")

    if "takeaways" in final and isinstance(final["takeaways"], list):
        lines.append("## Takeaways")
        for t in final["takeaways"]:
            lines.append(f"- {t}")
        lines.append("")

    if "freitext" in final:
        lines.append("## Zusammenfassung (Freitext)")
        lines.append(final["freitext"])

    with open(path, "w", encoding="utf-8") as f:
        f.write("\n".join(lines))
    logging.info(f"Markdown gespeichert: {path}")


# ----------------------------- CLI -----------------------------

def main():
    ap = argparse.ArgumentParser(
        description="Video transkribieren und mit lokalem LLM zusammenfassen (Map-Reduce)."
    )
    ap.add_argument("video", help="Pfad zur Video-Datei (mp4, mkv, …)")
    ap.add_argument("--out-dir", default="outputs", help="Ausgabe-Ordner")

    # Whisper
    ap.add_argument("--whisper-model", default="large-v3", help="faster-whisper Modellname")
    ap.add_argument(
        "--device", choices=["auto", "cpu", "cuda"], default="auto", help="Gerät für Whisper"
    )
    ap.add_argument(
        "--compute-type",
        choices=["int8", "int8_float16", "int16", "float16", "float32"],
        default=None,
        help="Rechenmodus (Standard: float16 auf GPU, int8 auf CPU)",
    )
    ap.add_argument("--language", default=None, help="z.B. 'de' oder 'en' – sonst Auto-Detect")

    # Chunking
    ap.add_argument("--target-chunk-tokens", type=int, default=1600, help="Ziel Tokens je Chunk")
    ap.add_argument("--hard-max-tokens", type=int, default=2048, help="Harte Token-Grenze je Segment/Chunk")

    # LLM
    ap.add_argument("--llm-model", default="llama3.1:8b", help="Ollama Modellname")
    ap.add_argument("--num-ctx", type=int, default=8192, help="Kontextfenster für LLM")
    ap.add_argument("--map-num-predict", type=int, default=700, help="Max Tokens pro Map-Summary")
    ap.add_argument(
        "--reduce-num-predict", type=int, default=900, help="Max Tokens für finale Zusammenfassung"
    )
    ap.add_argument("--temperature", type=float, default=0.2, help="LLM-Temperatur")

    # Sonstiges
    ap.add_argument("--no-srt", action="store_true", help="Kein SRT speichern")
    ap.add_argument("--keep-audio", action="store_true", help="Audio nach Transkript behalten")
    ap.add_argument("-v", "--verbose", action="store_true", help="mehr Logausgabe")

    args = ap.parse_args()
    setup_logging(args.verbose)

    try:
        os.makedirs(args.out_dir, exist_ok=True)

        # 1) Audio extrahieren
        audio_path = os.path.join(args.out_dir, "temp_audio.wav")
        run_ffmpeg_extract_audio(args.video, audio_path)

        # 2) Transkribieren
        segments = transcribe_audio(
            audio_path,
            whisper_model=args.whisper_model,
            device=args.device,
            compute_type=args.compute_type,
            language=args.language,
        )

        logging.info(f"Segmente erhalten: {len(segments)}")

        # Debug: Zeige erste paar Segmente an (mit verbesserter Fehlerbehandlung)
        try:
            logging.info("Beginne Segment-Anzeige...")
            for i, seg in enumerate(segments[:3]):
                try:
                    # Sicherere Text-Behandlung
                    safe_text = str(seg.text)[:80].replace('\n', ' ').replace('\r', ' ')
                    # Entferne problematische Unicode-Zeichen
                    safe_text = safe_text.encode('utf-8', errors='ignore').decode('utf-8')
                    logging.info(f"SEG[{i}] {seg.start:.1f}-{seg.end:.1f}: {safe_text}")
                except Exception as seg_error:
                    logging.warning(f"Fehler bei Segment {i}: {seg_error}")
                    logging.info(f"SEG[{i}] {seg.start:.1f}-{seg.end:.1f}: <Textanzeige fehlgeschlagen>")
            logging.info("Segment-Anzeige abgeschlossen.")
        except Exception as e:
            logging.error(f"Kritischer Fehler beim Anzeigen der Segmente: {e}")
            import traceback
            logging.error(traceback.format_exc())

        # 3) Speichern (SRT/TXT/JSON)
        try:
            base = os.path.splitext(os.path.basename(args.video))[0]
            srt_path = os.path.join(args.out_dir, f"{base}.srt")
            txt_path = os.path.join(args.out_dir, f"{base}.transcript.txt")
            json_path = os.path.join(args.out_dir, f"{base}.segments.json")

            logging.info("Starte Speichern…")

            if not args.no_srt:
                logging.info("Speichere SRT-Datei...")
                write_srt(segments, srt_path)

            logging.info("Speichere TXT-Datei...")
            write_transcript_txt(segments, txt_path)

            logging.info("Speichere JSON-Datei...")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump([s.__dict__ for s in segments], f, ensure_ascii=False, indent=2)
            logging.info(f"Segmente JSON gespeichert: {json_path}")

        except Exception as save_error:
            logging.error(f"Fehler beim Speichern der Dateien: {save_error}")
            import traceback
            logging.error(traceback.format_exc())
            raise

        # 4) Chunking
        logging.info("Beginne Chunking...")
        chunks = build_chunks_from_segments(
            segments,
            target_chunk_tokens=args.target_chunk_tokens,
            hard_max_tokens=args.hard_max_tokens,
        )

        # 5) Map-Reduce Summaries (lokales LLM via Ollama)
        logging.info("Beginne Map-Reduce Zusammenfassung...")
        result = map_reduce_summarize(
            chunks,
            llm_model=args.llm_model,
            num_ctx=args.num_ctx,
            map_num_predict=args.map_num_predict,
            reduce_num_predict=args.reduce_num_predict,
            temperature=args.temperature,
        )

        # 6) Speichern der Ergebnisse
        result_json_path = os.path.join(args.out_dir, f"{base}.summary.json")
        with open(result_json_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logging.info(f"Ergebnisse JSON gespeichert: {result_json_path}")

        md_path = os.path.join(args.out_dir, f"{base}.summary.md")
        write_markdown_summary(result, md_path)

        # 7) Aufräumen
        if not args.keep_audio and os.path.exists(audio_path):
            try:
                os.remove(audio_path)
                logging.info("Temporäre Audio-Datei entfernt")
            except OSError as e:
                logging.warning(f"Konnte temporäre Audio-Datei nicht entfernen: {e}")

        logging.info("Fertig ✅")

    except Exception as e:
        logging.error(f"Unerwarteter Fehler: {e}")
        logging.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()